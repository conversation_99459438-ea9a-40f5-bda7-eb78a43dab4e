"""
API客户端
用于与服务端进行数据同步
"""

import requests
import json
import logging
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class APIClient:
    """API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        # 修复URL构造逻辑
        if endpoint.startswith('/'):
            url = self.base_url + endpoint
        else:
            url = f"{self.base_url}/{endpoint}"

        # 调试日志
        logger.info(f"API请求: {method} {url}")

        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # 检查响应状态
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"API请求失败: {method} {url} -> {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': None
                }
            
            # 解析JSON响应
            try:
                data = response.json()
                return data
            except json.JSONDecodeError:
                return {
                    'success': True,
                    'message': 'Success',
                    'data': response.text
                }
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            logger.error(f"API请求超时: {url}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
        except requests.exceptions.ConnectionError:
            error_msg = "连接服务端失败，请检查服务端是否启动"
            logger.error(f"连接失败: {url}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            logger.error(f"API请求异常: {error_msg}")
            return {
                'success': False,
                'message': error_msg,
                'data': None
            }
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求"""
        return self._make_request('POST', endpoint, json=data)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """PUT请求"""
        return self._make_request('PUT', endpoint, json=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """DELETE请求"""
        return self._make_request('DELETE', endpoint)
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            response = self.get('/')
            return response.get('success', True)
        except:
            return False

class CategoryAPIClient:
    """分类API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_categories(self, page: int = 1, page_size: int = 20,
                      search: Optional[str] = None, series_id: Optional[str] = None) -> Dict[str, Any]:
        """获取分类列表"""
        try:
            # 使用管理端专用的categories API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search
            if series_id:
                params['series_id'] = series_id

            response = self.client.get('/api/categories', params=params)

            # 适配普通API返回格式，转换为管理端期望的格式
            logger.info(f"分类API响应类型: {type(response)}")

            # 处理直接返回数组的情况
            if isinstance(response, list):
                total = len(response)
                start = (page - 1) * page_size
                end = start + page_size
                paginated_data = response[start:end]

                return {
                    'success': True,
                    'data': paginated_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': (total + page_size - 1) // page_size,
                        'has_next': end < total,
                        'has_prev': page > 1
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                if 'pagination' in response:
                    return response

                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取分类列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                data = response.get('data', response)
                if isinstance(data, list):
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            else:
                return {
                    'success': False,
                    'message': f'获取分类列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取分类列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_category(self, category_id: str) -> Dict[str, Any]:
        """获取分类详情"""
        return self.client.get(f'/api/categories/{category_id}')
    
    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建分类"""
        # 转换字段名
        api_data = {
            'title': category_data.get('title'),
            'seriesId': str(category_data.get('series_id')),
            'price': float(category_data.get('price', 0)),
            'description': category_data.get('description'),
            'order_index': int(category_data.get('order_index', 0)),
            'isFree': category_data.get('price', 0) == 0
        }
        
        return self.client.post('/api/categories', data=api_data)
    
    def update_category(self, category_id: str, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新分类"""
        # 转换字段名
        api_data = {}
        
        if 'title' in category_data:
            api_data['title'] = category_data['title']
        if 'series_id' in category_data:
            api_data['seriesId'] = str(category_data['series_id'])
        if 'price' in category_data:
            api_data['price'] = float(category_data['price'])
            api_data['isFree'] = category_data['price'] == 0
        if 'description' in category_data:
            api_data['description'] = category_data['description']
        if 'order_index' in category_data:
            api_data['order_index'] = int(category_data['order_index'])
        
        return self.client.put(f'/api/categories/{category_id}', data=api_data)
    
    def delete_category(self, category_id: str) -> Dict[str, Any]:
        """删除分类"""
        return self.client.delete(f'/api/categories/{category_id}')

class SeriesAPIClient:
    """系列API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_series(self, page: int = 1, page_size: int = 20,
                  search: Optional[str] = None) -> Dict[str, Any]:
        """获取系列列表"""
        try:
            # 使用管理端专用的series API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search

            response = self.client.get('/api/series', params=params)

            # 适配普通API返回格式，转换为管理端期望的格式
            logger.info(f"API响应类型: {type(response)}, 内容: {str(response)[:200]}")

            # 处理直接返回数组的情况（服务端常见格式）
            if isinstance(response, list):
                # 直接是数组，进行分页处理
                total = len(response)
                start = (page - 1) * page_size
                end = start + page_size
                paginated_data = response[start:end]

                return {
                    'success': True,
                    'data': paginated_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': (total + page_size - 1) // page_size,
                        'has_next': end < total,
                        'has_prev': page > 1
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                # 检查是否已经是标准管理端格式
                if 'pagination' in response:
                    return response

                # 检查是否有success字段且为False
                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取系列列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                # 提取数据部分
                data = response.get('data', response)
                if isinstance(data, list):
                    # 数据在data字段中
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    # 单个对象，包装成数组
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            # 其他情况，返回失败
            else:
                return {
                    'success': False,
                    'message': f'获取系列列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取系列列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }

class VideoAPIClient:
    """视频API客户端"""
    
    def __init__(self, api_client: APIClient):
        self.client = api_client
    
    def get_videos(self, page: int = 1, page_size: int = 20,
                  search: Optional[str] = None, category_id: Optional[str] = None,
                  series_id: Optional[str] = None) -> Dict[str, Any]:
        """获取视频列表"""
        try:
            # 使用管理端专用的videos API
            params = {
                'page': page,
                'page_size': page_size
            }
            if search:
                params['search'] = search
            if category_id:
                params['category_id'] = category_id
            if series_id:
                params['series_id'] = series_id

            response = self.client.get('/api/videos', params=params)

            # 适配普通API返回格式，转换为管理端期望的格式
            logger.info(f"视频API响应类型: {type(response)}")

            # 处理直接返回数组的情况
            if isinstance(response, list):
                total = len(response)
                start = (page - 1) * page_size
                end = start + page_size
                paginated_data = response[start:end]

                return {
                    'success': True,
                    'data': paginated_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': (total + page_size - 1) // page_size,
                        'has_next': end < total,
                        'has_prev': page > 1
                    }
                }

            # 处理字典格式
            elif isinstance(response, dict):
                if 'pagination' in response:
                    return response

                if response.get('success') == False:
                    return {
                        'success': False,
                        'message': response.get('message', '获取视频列表失败'),
                        'data': [],
                        'pagination': {}
                    }

                data = response.get('data', response)
                if isinstance(data, list):
                    total = len(data)
                    start = (page - 1) * page_size
                    end = start + page_size
                    paginated_data = data[start:end]

                    return {
                        'success': True,
                        'data': paginated_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_records': total,
                            'total_pages': (total + page_size - 1) // page_size,
                            'has_next': end < total,
                            'has_prev': page > 1
                        }
                    }
                else:
                    return {
                        'success': True,
                        'data': [data] if data else [],
                        'pagination': {
                            'current_page': 1,
                            'page_size': page_size,
                            'total_records': 1 if data else 0,
                            'total_pages': 1 if data else 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    }

            else:
                return {
                    'success': False,
                    'message': f'获取视频列表失败: 未知响应格式 {type(response)}',
                    'data': [],
                    'pagination': {}
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取视频列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }


# 全局API客户端实例
def create_api_clients():
    """创建API客户端实例"""
    try:
        from config.api_config import api_config
        base_url = api_config.api_base_url
        timeout = api_config.api_timeout
    except:
        base_url = "http://localhost:8000"
        timeout = 30

    client = APIClient(base_url, timeout)
    return client, CategoryAPIClient(client), SeriesAPIClient(client), VideoAPIClient(client)

api_client, category_api, series_api, video_api = create_api_clients()
