#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DECIMAL, Boolean, TIMESTAMP, ForeignKey, Enum, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List

Base = declarative_base()

class User(Base):
    """用户模型"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), nullable=False, unique=True, comment='用户名')
    email = Column(String(100), nullable=False, unique=True, comment='邮箱')
    password_hash = Column(String(255), nullable=False, comment='密码哈希')
    is_active = Column(Boolean, default=True, comment='是否激活')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), 
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    orders = relationship("Order", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Series(Base):
    """系列模型"""
    __tablename__ = 'series'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False, comment='标题')
    description = Column(Text, comment='描述')
    price = Column(DECIMAL(10, 2), default=0.00, comment='价格')
    is_published = Column(Boolean, default=False, comment='是否发布')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), 
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    categories = relationship("Category", back_populates="series", cascade="all, delete-orphan")
    orders = relationship("Order", back_populates="series", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Series(id={self.id}, title='{self.title}', price={self.price})>"
    
    def to_dict(self):
        """转换为字典"""
        # 动态计算系列总价格（所有分类价格之和）
        total_price = sum(float(category.price or 0) for category in self.categories) if self.categories else 0.0

        # 计算总视频数
        total_videos = sum(len(category.videos) for category in self.categories) if self.categories else 0

        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'price': total_price,  # 动态计算的总价格
            'is_published': self.is_published,
            'category_count': len(self.categories) if self.categories else 0,
            'video_count': total_videos,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Category(Base):
    """分类模型"""
    __tablename__ = 'categories'

    id = Column(Integer, primary_key=True, autoincrement=True)
    series_id = Column(Integer, ForeignKey('series.id'), nullable=False, comment='所属系列ID')
    title = Column(String(200), nullable=False, comment='分类标题')
    description = Column(Text, comment='分类描述')
    price = Column(DECIMAL(10, 2), default=0.00, comment='分类价格')
    order_index = Column(Integer, default=0, comment='排序序号')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')

    # 关系
    series = relationship("Series", back_populates="categories")
    videos = relationship("Video", back_populates="category", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Category(id={self.id}, title='{self.title}', series_id={self.series_id}, price={self.price})>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'series_id': self.series_id,
            'series_title': self.series.title if self.series else None,
            'title': self.title,
            'description': self.description,
            'price': float(self.price) if self.price else 0.0,
            'order_index': self.order_index,
            'video_count': len(self.videos) if self.videos else 0,
            'total_duration': sum(video.duration or 0 for video in self.videos) if self.videos else 0,
            'total_duration_formatted': self.format_duration(sum(video.duration or 0 for video in self.videos) if self.videos else 0),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def format_duration(self, seconds):
        """格式化时长显示"""
        if not seconds:
            return "00:00"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

class Video(Base):
    """视频模型"""
    __tablename__ = 'videos'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=False, comment='所属分类ID')
    title = Column(String(200), nullable=False, comment='标题')
    description = Column(Text, comment='描述')
    video_url = Column(String(500), comment='视频地址')
    duration = Column(Integer, default=0, comment='时长(秒)')
    order_index = Column(Integer, default=0, comment='排序')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')

    # 关系
    category = relationship("Category", back_populates="videos")
    
    def __repr__(self):
        return f"<Video(id={self.id}, title='{self.title}', duration={self.duration})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'category_id': self.category_id,
            'category_title': self.category.title if self.category else None,
            'category_price': float(self.category.price) if self.category and self.category.price else 0.0,
            'series_id': self.category.series_id if self.category else None,
            'series_title': self.category.series.title if self.category and self.category.series else None,
            'title': self.title,
            'description': self.description,
            'video_url': self.video_url,
            'duration': self.duration,
            'duration_formatted': self.format_duration(),
            'order_index': self.order_index,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def format_duration(self):
        """格式化时长显示"""
        if not self.duration:
            return "00:00"
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

class Order(Base):
    """订单模型"""
    __tablename__ = 'orders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID')
    series_id = Column(Integer, ForeignKey('series.id'), nullable=True, comment='系列ID')
    amount = Column(DECIMAL(10, 2), nullable=False, comment='金额')
    status = Column(Enum('pending', 'completed', 'cancelled', name='order_status'), 
                   default='pending', comment='状态')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), 
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    user = relationship("User", back_populates="orders")
    series = relationship("Series", back_populates="orders")
    
    def __repr__(self):
        return f"<Order(id={self.id}, amount={self.amount}, status='{self.status}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'user_email': self.user.email if self.user else None,
            'series_id': self.series_id,
            'series_title': self.series.title if self.series else None,
            'amount': float(self.amount) if self.amount else 0.0,
            'status': self.status,
            'status_display': self.get_status_display(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_status_display(self):
        """获取状态显示文本"""
        status_map = {
            'pending': '待支付',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, connection_string: str):
        self.engine = create_engine(connection_string, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
        # 执行数据迁移
        self._migrate_data()

    def _migrate_data(self):
        """执行数据迁移"""
        try:
            session = self.get_session()

            # 检查是否需要迁移videos表
            try:
                result = session.execute("SHOW COLUMNS FROM videos LIKE 'category_id'")
                if not result.fetchone():
                    print("开始执行数据迁移...")

                    # 添加category_id字段
                    session.execute("ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL COMMENT '所属分类ID'")
                    session.execute("ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`)")

                    # 为现有系列创建默认分类
                    session.execute("""
                    INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
                    SELECT
                        `id` as `series_id`,
                        CONCAT(`title`, ' - 默认分类') as `title`,
                        '系统自动创建的默认分类' as `description`,
                        COALESCE(`price`, 0.00) as `price`,
                        1 as `order_index`
                    FROM `series`
                    WHERE NOT EXISTS (
                        SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
                    )
                    """)

                    # 将现有视频关联到默认分类
                    session.execute("""
                    UPDATE `videos` v
                    JOIN `categories` c ON c.series_id = v.series_id
                    SET v.category_id = c.id
                    WHERE v.category_id IS NULL
                    AND c.title LIKE '%默认分类%'
                    """)

                    session.commit()
                    print("✅ 数据迁移完成")
                else:
                    print("数据库已是最新版本")
            except Exception as e:
                print(f"迁移过程中出现错误: {e}")
                session.rollback()

            session.close()
        except Exception as e:
            print(f"数据迁移失败: {e}")
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        self.engine.dispose()
