#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据管理器
负责协调本地缓存和服务端数据的获取和同步
"""

import logging
from typing import Dict, Any, Optional
from .cache_manager import CacheManager

logger = logging.getLogger(__name__)

class DataManager:
    """统一数据管理器"""
    
    def __init__(self, cache_file_path: str = "./data/cache.json"):
        self.cache_manager = CacheManager(cache_file_path)
        self.data_store = {}  # 内存中的数据存储
        self._initialized = False
    
    def initialize(self, course_service, user_service=None) -> bool:
        """初始化数据管理器"""
        if self._initialized:
            return True

        try:
            logger.info("🚀 初始化数据管理器...")

            # 优先使用本地缓存
            if self._load_from_cache():
                logger.info("✅ 使用本地缓存数据")
                self._initialized = True
                return True

            # 本地缓存不可用，从服务端获取
            logger.info("📡 本地缓存不可用，从服务端获取数据...")
            try:
                if self._load_from_server(course_service, user_service):
                    logger.info("✅ 从服务端获取数据成功")
                    self._initialized = True
                    return True
                else:
                    logger.warning("⚠️ 从服务端获取数据失败，使用空数据初始化")
                    # 使用空数据初始化，避免完全失败
                    self.data_store = {
                        'series': [],
                        'categories': [],
                        'videos': [],
                        'users': []
                    }
                    self._initialized = True
                    return True
            except Exception as e:
                logger.error(f"❌ 服务端数据获取异常: {e}")
                # 降级处理：使用空数据
                self.data_store = {
                    'series': [],
                    'categories': [],
                    'videos': [],
                    'users': []
                }
                self._initialized = True
                return True

        except Exception as e:
            logger.error(f"❌ 初始化数据管理器失败: {e}")
            return False
    
    def get_data(self, data_type: str, **kwargs) -> Dict[str, Any]:
        """获取数据（统一接口）"""
        try:
            if not self._initialized:
                logger.warning("数据管理器未初始化，尝试自动初始化...")
                # 尝试从缓存加载
                if self._load_from_cache():
                    self._initialized = True
                    logger.info("自动初始化成功，使用缓存数据")
                else:
                    return {
                        'success': False,
                        'message': '数据管理器未初始化且无可用缓存',
                        'data': [],
                        'pagination': {}
                    }
            
            # 从内存获取数据
            data = self.data_store.get(data_type, [])
            
            # 应用筛选和分页
            filtered_data = self._apply_filters(data, **kwargs)
            paginated_result = self._apply_pagination(filtered_data, **kwargs)
            
            return paginated_result
            
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return {
                'success': False,
                'message': f'获取数据失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def update_data(self, data_type: str, item_id: int, updated_data: Dict[str, Any]) -> bool:
        """更新数据（同时更新内存和缓存）"""
        try:
            if data_type not in self.data_store:
                return False
            
            # 更新内存中的数据
            data_list = self.data_store[data_type]
            for i, item in enumerate(data_list):
                if item.get('id') == item_id:
                    data_list[i].update(updated_data)
                    break
            
            # 保存到缓存
            self.cache_manager.save_cache(self.data_store)
            logger.info(f"数据更新成功: {data_type} ID={item_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新数据失败: {e}")
            return False
    
    def add_data(self, data_type: str, new_data: Dict[str, Any]) -> bool:
        """添加数据（同时更新内存和缓存）"""
        try:
            if data_type not in self.data_store:
                self.data_store[data_type] = []
            
            # 添加到内存
            self.data_store[data_type].append(new_data)
            
            # 保存到缓存
            self.cache_manager.save_cache(self.data_store)
            logger.info(f"数据添加成功: {data_type}")
            return True
            
        except Exception as e:
            logger.error(f"添加数据失败: {e}")
            return False
    
    def delete_data(self, data_type: str, item_id: int) -> bool:
        """删除数据（同时更新内存和缓存）"""
        try:
            if data_type not in self.data_store:
                return False
            
            # 从内存中删除
            data_list = self.data_store[data_type]
            self.data_store[data_type] = [item for item in data_list if item.get('id') != item_id]
            
            # 保存到缓存
            self.cache_manager.save_cache(self.data_store)
            logger.info(f"数据删除成功: {data_type} ID={item_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除数据失败: {e}")
            return False
    
    def _load_from_cache(self) -> bool:
        """从本地缓存加载数据"""
        try:
            # 检查缓存是否可用
            if not self.cache_manager.has_data('series'):
                return False
            
            # 加载各类型数据到内存
            self.data_store['series'] = self.cache_manager.get_data('series') or []
            self.data_store['categories'] = self.cache_manager.get_data('categories') or []
            self.data_store['videos'] = self.cache_manager.get_data('videos') or []
            self.data_store['users'] = self.cache_manager.get_data('users') or []
            
            logger.info(f"从缓存加载数据: 系列={len(self.data_store['series'])}, "
                       f"分类={len(self.data_store['categories'])}, "
                       f"视频={len(self.data_store['videos'])}, "
                       f"用户={len(self.data_store['users'])}")
            
            return True
            
        except Exception as e:
            logger.error(f"从缓存加载数据失败: {e}")
            return False
    
    def _load_from_server(self, course_service, user_service=None) -> bool:
        """从服务端加载数据"""
        try:
            logger.info("📡 开始从服务端获取数据...")

            # 获取系列数据（绕过缓存，直接从API获取）
            course_service_temp = course_service
            course_service_temp.use_cache = False  # 临时禁用缓存避免循环

            series_result = course_service_temp.get_series_list(page=1, page_size=1000)
            if not series_result.get('success'):
                logger.error(f"获取系列数据失败: {series_result.get('message')}")
                return False

            # 获取分类数据
            categories_result = course_service_temp.get_category_list(page=1, page_size=1000)
            if not categories_result.get('success'):
                logger.error(f"获取分类数据失败: {categories_result.get('message')}")
                return False

            # 获取视频数据
            videos_result = course_service_temp.get_video_list(page=1, page_size=1000)
            if not videos_result.get('success'):
                logger.error(f"获取视频数据失败: {videos_result.get('message')}")
                return False

            # 恢复缓存设置
            course_service_temp.use_cache = True

            # 获取用户数据（如果提供了用户服务）
            users_data = []
            if user_service:
                try:
                    # 临时禁用用户服务的缓存，避免循环
                    user_service_temp = user_service
                    user_service_temp.use_cache = False

                    users_result = user_service_temp.get_user_list(page=1, page_size=1000)
                    if users_result.get('success'):
                        users_data = users_result.get('data', [])
                        logger.info(f"获取用户数据成功: {len(users_data)} 条")
                    else:
                        logger.warning(f"获取用户数据失败: {users_result.get('message')}")

                    # 恢复缓存设置
                    user_service_temp.use_cache = True
                except Exception as e:
                    logger.warning(f"获取用户数据异常: {e}")

            # 存储到内存
            self.data_store = {
                'series': series_result.get('data', []),
                'categories': categories_result.get('data', []),
                'videos': videos_result.get('data', []),
                'users': users_data
            }

            # 保存到缓存
            self.cache_manager.save_cache(self.data_store)

            logger.info(f"从服务端获取数据成功: 系列={len(self.data_store['series'])}, "
                       f"分类={len(self.data_store['categories'])}, "
                       f"视频={len(self.data_store['videos'])}, "
                       f"用户={len(self.data_store['users'])}")

            return True

        except Exception as e:
            logger.error(f"从服务端加载数据失败: {e}")
            return False
    
    def _apply_filters(self, data: list, **kwargs) -> list:
        """应用筛选条件"""
        filtered_data = data.copy()
        
        # 搜索筛选
        search = kwargs.get('search')
        if search:
            filtered_data = [
                item for item in filtered_data
                if search.lower() in item.get('title', '').lower()
            ]
        
        # 系列ID筛选
        series_id = kwargs.get('series_id')
        if series_id:
            filtered_data = [
                item for item in filtered_data
                if item.get('series_id') == series_id
            ]
        
        # 分类ID筛选
        category_id = kwargs.get('category_id')
        if category_id:
            filtered_data = [
                item for item in filtered_data
                if item.get('category_id') == category_id
            ]
        
        # 发布状态筛选
        is_published = kwargs.get('is_published')
        if is_published is not None:
            filtered_data = [
                item for item in filtered_data
                if item.get('is_published') == is_published
            ]
        
        return filtered_data
    
    def _apply_pagination(self, data: list, **kwargs) -> Dict[str, Any]:
        """应用分页"""
        page = kwargs.get('page', 1)
        page_size = kwargs.get('page_size', 20)
        
        total_records = len(data)
        total_pages = (total_records + page_size - 1) // page_size
        
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        
        paginated_data = data[start_index:end_index]
        
        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        }
