#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
课程业务服务
"""

from typing import List, Optional, Dict, Any, Tuple
from database.dao import SeriesDAO, CategoryDAO, VideoDAO
from database.models import DatabaseManager
from cache.data_manager import DataManager
from cache.global_data_manager import global_data_manager
from cache.sync_manager import sync_manager
import logging

# 尝试导入API客户端，如果失败则使用本地数据库
try:
    from api.client import category_api, series_api, video_api
    API_AVAILABLE = True
except ImportError:
    API_AVAILABLE = False

logger = logging.getLogger(__name__)

class CourseService:
    """课程业务服务类"""

    def __init__(self, db_manager: DatabaseManager, use_api: bool = False, use_cache: bool = True):
        self.db_manager = db_manager
        self.use_api = use_api and API_AVAILABLE
        self.use_cache = use_cache

        # 初始化数据管理器
        if self.use_cache:
            self.data_manager = DataManager()
            logger.info("开发阶段：强制API模式，跳过缓存优先")
        else:
            self.data_manager = None

        if self.use_api:
            logger.info("开发阶段：强制使用API模式获取最新数据")
        else:
            logger.info("使用本地数据库模式")

        # 初始化ID映射表
        self._series_id_mapping = {}  # 字符串ID -> 整数ID 的映射
        self._reverse_series_mapping = {}  # 整数ID -> 字符串ID 的映射

        # 初始化同步管理器
        if self.use_api:
            try:
                from api.client import series_api, category_api, video_api
                sync_manager.set_api_clients(series_api, category_api, video_api)
                print("✅ 同步管理器API客户端已设置")
            except Exception as e:
                print(f"⚠️ 同步管理器API客户端设置失败: {e}")

    def initialize_data(self, user_service=None) -> bool:
        """初始化数据（仅在程序启动时调用一次）"""
        if self.use_cache and self.data_manager:
            return self.data_manager.initialize(self, user_service)
        return True

    def load_all_data_once(self) -> bool:
        """一次性加载所有数据到内存"""
        print("🚀 CourseService: 开始一次性加载所有数据...")
        return global_data_manager.load_all_data_once()
    
    # ==================== 系列管理 ====================
    
    def get_series_list(self, page: int = 1, page_size: int = 20,
                       search: str = None, is_published: bool = None) -> Dict[str, Any]:
        """获取系列列表"""
        try:
            # 开发阶段：强制使用API获取最新数据，跳过缓存
            # if self.use_cache and self.data_manager and self.data_manager._initialized:
            #     logger.info("使用缓存获取系列列表")
            #     return self.data_manager.get_data('series', page=page, page_size=page_size,
            #                                     search=search, is_published=is_published)

            # 优先使用全局数据管理器（内存数据）
            if global_data_manager.is_data_loaded():
                print("📚 从内存获取系列列表（无网络请求）")
                return global_data_manager.get_series_list(page, page_size, search)

            # 如果内存数据未加载，降级到原有逻辑
            print("⚠️ 内存数据未加载，降级到API请求")
            if self.use_api:
                logger.info("🔥 开发阶段：强制从API获取系列列表（跳过缓存）")
                return series_api.get_series(page, page_size, search)
            else:
                # 使用本地数据库模式
                logger.info("使用本地数据库获取系列列表")
                session = self.db_manager.get_session()
                series_dao = SeriesDAO(session)

                series_list, total = series_dao.get_all(page, page_size, search, is_published)

                # 转换为字典格式
                series_data = [series.to_dict() for series in series_list]

                # 计算分页信息
                total_pages = (total + page_size - 1) // page_size

                session.close()

                return {
                    'success': True,
                    'data': series_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                }
        except Exception as e:
            logger.error(f"获取系列列表失败: {e}")
            return {
                'success': False,
                'message': f'获取系列列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_series_detail(self, series_id: int) -> Dict[str, Any]:
        """获取系列详情"""
        try:
            session = self.db_manager.get_session()
            series_dao = SeriesDAO(session)
            
            series = series_dao.get_by_id(series_id)
            if not series:
                return {
                    'success': False,
                    'message': '系列不存在',
                    'data': None
                }
            
            # 获取系列分类和视频
            category_dao = CategoryDAO(session)
            categories = category_dao.get_by_series_id(series_id)
            category_list = []
            total_videos = 0
            total_duration = 0

            for category in categories:
                category_dict = category.to_dict()
                videos = category_dao.get_category_videos(category.id)
                category_dict['videos'] = [video.to_dict() for video in videos]
                category_list.append(category_dict)
                total_videos += len(videos)
                total_duration += sum(video.duration or 0 for video in videos)

            series_data = series.to_dict()
            series_data['categories'] = category_list
            series_data['category_count'] = len(category_list)
            series_data['video_count'] = total_videos
            series_data['total_duration'] = total_duration
            series_data['total_duration_formatted'] = self._format_duration(total_duration)
            
            session.close()
            
            return {
                'success': True,
                'data': series_data,
                'message': '获取系列详情成功'
            }
        except Exception as e:
            logger.error(f"获取系列详情失败: {e}")
            return {
                'success': False,
                'message': f'获取系列详情失败: {str(e)}',
                'data': None
            }
    
    def create_series(self, series_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建系列"""
        try:
            # 数据验证
            validation_result = self._validate_series_data(series_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            series_dao = SeriesDAO(session)
            
            # 创建系列
            series = series_dao.create(series_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '系列创建成功',
                'data': series.to_dict()
            }
        except Exception as e:
            logger.error(f"创建系列失败: {e}")
            return {
                'success': False,
                'message': f'创建系列失败: {str(e)}',
                'data': None
            }
    
    def update_series(self, series_id: int, series_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新系列 - 异步三重同步"""
        session = None
        try:
            print(f"🚀 开始异步三重同步更新系列 {series_id}")

            # 数据验证
            validation_result = self._validate_series_data(series_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }

            session = self.db_manager.get_session()
            series_dao = SeriesDAO(session)

            # 调试：检查ID类型和值
            print(f"🔍 调试信息: series_id={repr(series_id)}, type={type(series_id)}")

            # 建立字符串ID到整数ID的映射
            db_series_id = self._get_or_create_series_mapping(series_id, series_data)
            print(f"🔍 映射结果: 字符串ID={repr(series_id)} → 数据库ID={db_series_id}")

            # 检查系列是否存在（使用数据库ID）
            existing_series = series_dao.get_by_id(db_series_id)
            print(f"🔍 数据库查询结果: {existing_series is not None}")

            if not existing_series:
                return {
                    'success': False,
                    'message': f'系列不存在 (字符串ID: {series_id}, 数据库ID: {db_series_id})',
                    'data': None
                }

            # 第一阶段：立即更新本地数据（用户立即看到效果）
            print(f"📊 第一阶段：更新本地数据库...")
            series = series_dao.update(series_id, series_data)
            session.commit()
            print(f"✅ 本地数据库更新成功")

            # 第二阶段：立即更新内存缓存
            print(f"💾 第二阶段：更新内存缓存...")
            if global_data_manager.is_data_loaded():
                global_data_manager.update_series_in_memory(str(series_id), series_data)
                print(f"✅ 内存缓存更新成功")
            else:
                print(f"⚠️ 内存数据未加载，跳过内存缓存更新")

            # 立即返回成功（用户无需等待服务器同步）
            result = {
                'success': True,
                'message': '系列更新成功（本地已生效）',
                'data': series.to_dict() if series else None,
                'sync_status': 'local_updated'  # 本地已更新
            }

            # 第三阶段：异步同步到服务器
            if self.use_api:
                print(f"🌐 第三阶段：异步同步到服务器...")
                sync_manager.async_sync_to_server('series', str(series_id), series_data)

            return result

        except Exception as e:
            # 发生异常，回滚本地更改
            if session:
                session.rollback()
            logger.error(f"更新系列失败: {e}")
            return {
                'success': False,
                'message': f'更新系列失败: {str(e)}',
                'data': None
            }
        finally:
            if session:
                session.close()
    
    def delete_series(self, series_id: int) -> Dict[str, Any]:
        """删除系列"""
        try:
            session = self.db_manager.get_session()
            series_dao = SeriesDAO(session)
            
            # 检查系列是否存在
            series = series_dao.get_by_id(series_id)
            if not series:
                session.close()
                return {
                    'success': False,
                    'message': '系列不存在'
                }
            
            # 检查是否有关联的分类和视频
            category_dao = CategoryDAO(session)
            categories = category_dao.get_by_series_id(series_id)
            if categories:
                total_videos = sum(len(category_dao.get_category_videos(cat.id)) for cat in categories)
                session.close()
                return {
                    'success': False,
                    'message': f'无法删除系列，还有 {len(categories)} 个分类和 {total_videos} 个视频'
                }
            
            # 执行删除
            success = series_dao.delete(series_id)
            message = '系列删除成功' if success else '系列删除失败'
            
            session.close()
            
            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除系列失败: {e}")
            return {
                'success': False,
                'message': f'删除系列失败: {str(e)}'
            }
    
    def toggle_series_publish(self, series_id: int) -> Dict[str, Any]:
        """切换系列发布状态"""
        try:
            session = self.db_manager.get_session()
            series_dao = SeriesDAO(session)
            
            series = series_dao.get_by_id(series_id)
            if not series:
                session.close()
                return {
                    'success': False,
                    'message': '系列不存在'
                }
            
            # 切换发布状态
            new_status = not series.is_published
            series_dao.update(series_id, {'is_published': new_status})
            
            session.close()
            
            status_text = '已发布' if new_status else '已下架'
            return {
                'success': True,
                'message': f'系列{status_text}成功',
                'data': {'is_published': new_status}
            }
        except Exception as e:
            logger.error(f"切换系列发布状态失败: {e}")
            return {
                'success': False,
                'message': f'操作失败: {str(e)}'
            }
    
    # ==================== 分类管理 ====================

    def get_category_list(self, page: int = 1, page_size: int = 20,
                         search: str = None, series_id: int = None) -> Dict[str, Any]:
        """获取分类列表"""
        try:
            # 开发阶段：强制使用API获取最新数据，跳过缓存
            # if self.use_cache and self.data_manager and self.data_manager._initialized:
            #     logger.info("使用缓存获取分类列表")
            #     return self.data_manager.get_data('categories', page=page, page_size=page_size,
            #                                     search=search, series_id=series_id)

            # 优先使用全局数据管理器（内存数据）
            if global_data_manager.is_data_loaded():
                print("📂 从内存获取分类列表（无网络请求）")
                processed_series_id = str(series_id) if series_id else None
                return global_data_manager.get_category_list(page, page_size, search, processed_series_id)

            # 如果内存数据未加载，降级到原有逻辑
            print("⚠️ 内存数据未加载，降级到API请求")
            if self.use_api:
                logger.info("🔥 开发阶段：强制从API获取分类列表（跳过缓存）")
                processed_series_id = str(series_id) if series_id else None
                print(f"🔍 CourseService 传递给API的参数: page={page}, page_size={page_size}, search={repr(search)}, series_id={repr(series_id)} -> {repr(processed_series_id)}")
                return category_api.get_categories(page, page_size, search, processed_series_id)
            else:
                # 使用本地数据库模式
                session = self.db_manager.get_session()
                category_dao = CategoryDAO(session)

                categories, total = category_dao.get_all(page, page_size, search, series_id)

                # 转换为字典格式
                category_data = [category.to_dict() for category in categories]

                # 计算分页信息
                total_pages = (total + page_size - 1) // page_size

                session.close()

                return {
                    'success': True,
                    'data': category_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                }
        except Exception as e:
            logger.error(f"获取分类列表失败: {e}")
            return {
                'success': False,
                'message': f'获取分类列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }

    def get_category_detail(self, category_id: int) -> Dict[str, Any]:
        """获取分类详情"""
        try:
            session = self.db_manager.get_session()
            category_dao = CategoryDAO(session)

            category = category_dao.get_by_id(category_id)
            if not category:
                return {
                    'success': False,
                    'message': '分类不存在',
                    'data': None
                }

            # 获取分类视频
            videos = category_dao.get_category_videos(category_id)
            video_list = [video.to_dict() for video in videos]

            category_data = category.to_dict()
            category_data['videos'] = video_list

            session.close()

            return {
                'success': True,
                'data': category_data,
                'message': '获取分类详情成功'
            }
        except Exception as e:
            logger.error(f"获取分类详情失败: {e}")
            return {
                'success': False,
                'message': f'获取分类详情失败: {str(e)}',
                'data': None
            }

    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建分类"""
        try:
            # 数据验证
            validation_result = self._validate_category_data(category_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }

            if self.use_api:
                # 使用API模式
                return category_api.create_category(category_data)
            else:
                # 使用本地数据库模式
                session = self.db_manager.get_session()
                category_dao = CategoryDAO(session)

                # 创建分类
                category = category_dao.create(category_data)

                session.close()

                return {
                    'success': True,
                    'message': '分类创建成功',
                    'data': category.to_dict()
                }
        except Exception as e:
            logger.error(f"创建分类失败: {e}")
            return {
                'success': False,
                'message': f'创建分类失败: {str(e)}',
                'data': None
            }

    def update_category(self, category_id: int, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新分类 - 异步三重同步"""
        session = None
        try:
            print(f"🚀 开始异步三重同步更新分类 {category_id}")

            # 数据验证
            validation_result = self._validate_category_data(category_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }

            session = self.db_manager.get_session()
            category_dao = CategoryDAO(session)

            # 检查分类是否存在
            existing_category = category_dao.get_by_id(category_id)
            if not existing_category:
                return {
                    'success': False,
                    'message': '分类不存在',
                    'data': None
                }

            # 第一阶段：立即更新本地数据（用户立即看到效果）
            print(f"📊 第一阶段：更新本地数据库...")
            category = category_dao.update(category_id, category_data)
            session.commit()
            print(f"✅ 本地数据库更新成功")

            # 第二阶段：立即更新内存缓存
            print(f"💾 第二阶段：更新内存缓存...")
            if global_data_manager.is_data_loaded():
                global_data_manager.update_category_in_memory(str(category_id), category_data)
                print(f"✅ 内存缓存更新成功")
            else:
                print(f"⚠️ 内存数据未加载，跳过内存缓存更新")

            # 立即返回成功（用户无需等待服务器同步）
            result = {
                'success': True,
                'message': '分类更新成功（本地已生效）',
                'data': category.to_dict() if category else None,
                'sync_status': 'local_updated'  # 本地已更新
            }

            # 第三阶段：异步同步到服务器
            if self.use_api:
                print(f"🌐 第三阶段：异步同步到服务器...")
                sync_manager.async_sync_to_server('category', str(category_id), category_data)

            return result

        except Exception as e:
            # 发生异常，回滚本地更改
            if session:
                session.rollback()
            logger.error(f"更新分类失败: {e}")
            return {
                'success': False,
                'message': f'更新分类失败: {str(e)}',
                'data': None
            }
        finally:
            if session:
                session.close()

    def delete_category(self, category_id: int) -> Dict[str, Any]:
        """删除分类"""
        try:
            session = self.db_manager.get_session()
            category_dao = CategoryDAO(session)

            # 检查分类是否存在
            category = category_dao.get_by_id(category_id)
            if not category:
                session.close()
                return {
                    'success': False,
                    'message': '分类不存在'
                }

            # 检查是否有关联的视频
            videos = category_dao.get_category_videos(category_id)
            if videos:
                session.close()
                return {
                    'success': False,
                    'message': f'无法删除分类，还有 {len(videos)} 个关联视频'
                }

            # 执行删除
            success = category_dao.delete(category_id)
            message = '分类删除成功' if success else '分类删除失败'

            session.close()

            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除分类失败: {e}")
            return {
                'success': False,
                'message': f'删除分类失败: {str(e)}'
            }

    # ==================== 视频管理 ====================
    
    def get_video_list(self, page: int = 1, page_size: int = 20,
                      search: str = None, category_id: int = None, series_id: int = None) -> Dict[str, Any]:
        """获取视频列表"""
        try:
            # 开发阶段：强制使用API获取最新数据，跳过缓存
            # if self.use_cache and self.data_manager and self.data_manager._initialized:
            #     logger.info("使用缓存获取视频列表")
            #     return self.data_manager.get_data('videos', page=page, page_size=page_size,
            #                                     search=search, category_id=category_id, series_id=series_id)

            # 优先使用全局数据管理器（内存数据）
            if global_data_manager.is_data_loaded():
                print("📹 从内存获取视频列表（无网络请求）")
                return global_data_manager.get_video_list(page, page_size, search,
                                                        str(series_id) if series_id else None,
                                                        str(category_id) if category_id else None)

            # 如果内存数据未加载，降级到原有逻辑
            print("⚠️ 内存数据未加载，降级到API请求")
            if self.use_api:
                logger.info("🔥 开发阶段：强制从API获取视频列表（跳过缓存）")
                return video_api.get_videos(page, page_size, search,
                                          str(category_id) if category_id else None,
                                          str(series_id) if series_id else None)
            else:
                # 使用本地数据库模式
                logger.info("使用本地数据库获取视频列表")
                session = self.db_manager.get_session()
                video_dao = VideoDAO(session)

                videos, total = video_dao.get_all(page, page_size, search, category_id, series_id)

                # 转换为字典格式
                video_data = [video.to_dict() for video in videos]

                # 计算分页信息
                total_pages = (total + page_size - 1) // page_size

                session.close()

                return {
                    'success': True,
                    'data': video_data,
                    'pagination': {
                        'current_page': page,
                        'page_size': page_size,
                        'total_records': total,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                }
        except Exception as e:
            logger.error(f"获取视频列表失败: {e}")
            return {
                'success': False,
                'message': f'获取视频列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def create_video(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建视频"""
        try:
            # 数据验证
            validation_result = self._validate_video_data(video_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            video_dao = VideoDAO(session)
            
            # 如果没有指定排序，设置为最后
            if 'order_index' not in video_data and video_data.get('category_id'):
                category_videos, _ = video_dao.get_all(page=1, page_size=10000, category_id=video_data['category_id'])
                video_data['order_index'] = len(category_videos) + 1
            
            # 创建视频
            video = video_dao.create(video_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '视频创建成功',
                'data': video.to_dict()
            }
        except Exception as e:
            logger.error(f"创建视频失败: {e}")
            return {
                'success': False,
                'message': f'创建视频失败: {str(e)}',
                'data': None
            }
    
    def update_video(self, video_id: int, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新视频 - 异步三重同步"""
        session = None
        try:
            print(f"🚀 开始异步三重同步更新视频 {video_id}")

            # 数据验证
            validation_result = self._validate_video_data(video_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }

            session = self.db_manager.get_session()
            video_dao = VideoDAO(session)

            # 第一阶段：立即更新本地数据（用户立即看到效果）
            print(f"📊 第一阶段：更新本地数据库...")
            video = video_dao.update(video_id, video_data)
            session.commit()
            print(f"✅ 本地数据库更新成功")

            # 第二阶段：立即更新内存缓存
            print(f"💾 第二阶段：更新内存缓存...")
            if global_data_manager.is_data_loaded():
                global_data_manager.update_video_in_memory(str(video_id), video_data)
                print(f"✅ 内存缓存更新成功")
            else:
                print(f"⚠️ 内存数据未加载，跳过内存缓存更新")

            # 立即返回成功（用户无需等待服务器同步）
            result = {
                'success': True,
                'message': '视频更新成功（本地已生效）',
                'data': video.to_dict() if video else None,
                'sync_status': 'local_updated'  # 本地已更新
            }

            # 第三阶段：异步同步到服务器
            if self.use_api:
                print(f"🌐 第三阶段：异步同步到服务器...")
                sync_manager.async_sync_to_server('video', str(video_id), video_data)

            return result

        except Exception as e:
            # 发生异常，回滚本地更改
            if session:
                session.rollback()
            logger.error(f"更新视频失败: {e}")
            return {
                'success': False,
                'message': f'更新视频失败: {str(e)}',
                'data': None
            }
        finally:
            if session:
                session.close()
    
    def delete_video(self, video_id: int) -> Dict[str, Any]:
        """删除视频"""
        try:
            session = self.db_manager.get_session()
            video_dao = VideoDAO(session)
            
            # 执行删除
            success = video_dao.delete(video_id)
            message = '视频删除成功' if success else '视频删除失败'
            
            session.close()
            
            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除视频失败: {e}")
            return {
                'success': False,
                'message': f'删除视频失败: {str(e)}'
            }
    
    # ==================== 辅助方法 ====================
    
    def _validate_series_data(self, series_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证系列数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['title']
            for field in required_fields:
                if field not in series_data or not series_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }
        
        # 标题验证
        if 'title' in series_data:
            title = series_data['title']
            if len(title) < 2 or len(title) > 200:
                return {
                    'valid': False,
                    'message': '标题长度必须在2-200个字符之间'
                }
        
        # 价格验证
        if 'price' in series_data:
            try:
                price = float(series_data['price'])
                if price < 0:
                    return {
                        'valid': False,
                        'message': '价格不能为负数'
                    }
            except (ValueError, TypeError):
                return {
                    'valid': False,
                    'message': '价格格式不正确'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
    
    def _validate_category_data(self, category_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证分类数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['title', 'series_id']
            for field in required_fields:
                if field not in category_data or not category_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }

        # 标题验证
        if 'title' in category_data:
            title = category_data['title']
            if len(title) < 2 or len(title) > 200:
                return {
                    'valid': False,
                    'message': '标题长度必须在2-200个字符之间'
                }

        # 价格验证
        if 'price' in category_data:
            try:
                price = float(category_data['price'])
                if price < 0:
                    return {
                        'valid': False,
                        'message': '价格不能为负数'
                    }
            except (ValueError, TypeError):
                return {
                    'valid': False,
                    'message': '价格格式不正确'
                }

        return {
            'valid': True,
            'message': '数据验证通过'
        }

    def _validate_video_data(self, video_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证视频数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['title', 'category_id']
            for field in required_fields:
                if field not in video_data or not video_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }
        
        # 标题验证
        if 'title' in video_data:
            title = video_data['title']
            if len(title) < 2 or len(title) > 200:
                return {
                    'valid': False,
                    'message': '标题长度必须在2-200个字符之间'
                }
        
        # 时长验证
        if 'duration' in video_data:
            try:
                duration = int(video_data['duration'])
                if duration < 0:
                    return {
                        'valid': False,
                        'message': '时长不能为负数'
                    }
            except (ValueError, TypeError):
                return {
                    'valid': False,
                    'message': '时长格式不正确'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
    
    def _format_duration(self, seconds: int) -> str:
        """格式化时长显示"""
        if not seconds:
            return "00:00"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
