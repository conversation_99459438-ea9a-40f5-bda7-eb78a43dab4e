{"cache_version": "1.0", "last_update": "2025-06-28T23:20:29.784576", "data": {"series": [{"id": "complete-package", "title": "全套课程", "icon": "crown", "iconColor": null, "price": 130000, "isFree": false, "isPurchased": false, "isPackage": true, "defaultExpanded": false, "totalVideos": 0, "categories": [], "category_count": 0, "video_count": 0, "is_published": true, "total_duration_formatted": "0分钟", "description": "暂无描述", "created_at": "", "updated_at": ""}, {"id": "free-series", "title": "免费精品系列", "icon": "play", "iconColor": null, "price": 0, "isFree": true, "isPurchased": false, "isPackage": false, "defaultExpanded": true, "totalVideos": 5, "categories": [{"id": "free-dating-tips", "title": "约会技巧", "seriesId": "free-series", "price": 0, "isFree": true, "isPurchased": true, "defaultExpanded": true, "totalVideos": 2, "videos": [{"id": "free-dating-prep", "title": "01. 约会前的准备工作", "description": "学习约会前的各种准备工作，包括形象打理、心理准备等。", "duration": 930, "categoryId": "free-dating-tips", "playCount": 12000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://vip.123pan.cn/1822199090/test_mock/%E6%8A%96%E9%9F%B3202569-395747.mp4", "localPath": null}, {"id": "free-dating-location", "title": "02. 约会地点的选择", "description": "如何选择合适的约会地点，营造浪漫氛围。", "duration": 765, "categoryId": "free-dating-tips", "playCount": 8500, "watch_count": 0, "cache_status": "CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video2.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}, {"id": "free-pickup-skills", "title": "搭讪技巧", "seriesId": "free-series", "price": 0, "isFree": true, "isPurchased": true, "defaultExpanded": false, "totalVideos": 3, "videos": [{"id": "free-pickup-mindset", "title": "01. 搭讪基础心态", "description": "建立正确的搭讪心态，克服内心恐惧。", "duration": 1100, "categoryId": "free-pickup-skills", "playCount": 21000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video3.mp4", "localPath": null}, {"id": "free-pickup-opening", "title": "02. 自然开场技巧", "description": "学习自然而然的开场白技巧。", "duration": 975, "categoryId": "free-pickup-skills", "playCount": 18000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video4.mp4", "localPath": null}, {"id": "free-pickup-anxiety", "title": "03. 克服紧张情绪", "description": "有效方法帮助你克服搭讪时的紧张情绪。", "duration": 870, "categoryId": "free-pickup-skills", "playCount": 15000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video5.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}], "category_count": 2, "video_count": 5, "is_published": true, "total_duration_formatted": "0分钟", "description": "暂无描述", "created_at": "", "updated_at": ""}, {"id": "love-guide-series", "title": "道：恋爱宝典系列", "icon": "heart", "iconColor": null, "price": 50000, "isFree": false, "isPurchased": false, "isPackage": false, "defaultExpanded": false, "totalVideos": 12, "categories": [{"id": "love-guide-1", "title": "恋爱宝典1", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": true, "defaultExpanded": false, "totalVideos": 11, "videos": [{"id": "love-guide-1-attraction", "title": "01. 初识吸引力法则", "description": "深入了解吸引力的本质和运作原理。", "duration": 1350, "categoryId": "love-guide-1", "playCount": 956, "watch_count": 3, "cache_status": "NOT_CACHED", "progress": 0.2, "cloudUrl": "https://example.com/videos/video6.mp4", "localPath": null}, {"id": "love-guide-1-confidence", "title": "02. 建立自信的方法", "description": "系统性方法帮助你建立内在自信。", "duration": 1185, "categoryId": "love-guide-1", "playCount": 1200, "watch_count": 6, "cache_status": "CACHED", "progress": 0.0625, "cloudUrl": "https://example.com/videos/video7.mp4", "localPath": null}, {"id": "love-guide-1-first-impression", "title": "03. 第一印象的重要性", "description": "如何在初次见面时留下深刻的好印象。", "duration": 1040, "categoryId": "love-guide-1", "playCount": 2300, "watch_count": 0, "cache_status": "CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video8.mp4", "localPath": null}, {"id": "love-guide-1-body-language", "title": "04. 肢体语言的艺术", "description": "掌握肢体语言在恋爱中的重要作用。", "duration": 1275, "categoryId": "love-guide-1", "playCount": 3100, "watch_count": 0, "cache_status": "CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video11.mp4", "localPath": null}, {"id": "love-guide-1-emotional-intelligence", "title": "05. 情商提升技巧", "description": "提高情商，更好地理解和处理情感。", "duration": 1110, "categoryId": "love-guide-1", "playCount": 4500, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video12.mp4", "localPath": null}, {"id": "love-guide-1-conversation-skills", "title": "06. 对话技巧进阶", "description": "掌握高级对话技巧，让聊天更有趣。", "duration": 1425, "categoryId": "love-guide-1", "playCount": 5200, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video13.mp4", "localPath": null}, {"id": "love-guide-1-date-planning", "title": "07. 约会策划大师", "description": "学会策划完美的约会，留下美好回忆。", "duration": 1220, "categoryId": "love-guide-1", "playCount": 6800, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video14.mp4", "localPath": null}, {"id": "love-guide-1-relationship-building", "title": "08. 关系建立与维护", "description": "建立稳固的恋爱关系并长期维护。", "duration": 1510, "categoryId": "love-guide-1", "playCount": 7300, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video15.mp4", "localPath": null}, {"id": "love-guide-1-conflict-resolution", "title": "09. 冲突解决智慧", "description": "学会处理恋爱中的矛盾和冲突。", "duration": 1170, "categoryId": "love-guide-1", "playCount": 8900, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video16.mp4", "localPath": null}, {"id": "love-guide-1-commitment", "title": "10. 承诺与责任", "description": "学会在恋爱中承担责任和承诺。", "duration": 1305, "categoryId": "love-guide-1", "playCount": 9400, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video17.mp4", "localPath": null}, {"id": "love-guide-1-future-planning", "title": "11. 未来规划共识", "description": "与伴侣共同规划美好的未来。", "duration": 1470, "categoryId": "love-guide-1", "playCount": 11000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video18.mp4", "localPath": null}], "progress": 0.023863636363636365, "watchCount": 9}, {"id": "love-guide-2", "title": "恋爱宝典2", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 1, "videos": [{"id": "love-guide-2-deep-communication", "title": "01. 深度沟通技巧", "description": "进阶的深度沟通技巧和方法。", "duration": 1575, "categoryId": "love-guide-2", "playCount": 18000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video19.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}, {"id": "love-guide-3", "title": "恋爱宝典3", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 0, "videos": [], "progress": 0.0, "watchCount": 0}, {"id": "love-guide-4", "title": "恋爱宝典4", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 0, "videos": [], "progress": 0.0, "watchCount": 0}, {"id": "love-guide-5", "title": "恋爱宝典5", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 0, "videos": [], "progress": 0.0, "watchCount": 0}, {"id": "love-guide-6", "title": "恋爱宝典6", "seriesId": "love-guide-series", "price": 10000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 0, "videos": [], "progress": 0.0, "watchCount": 0}], "category_count": 6, "video_count": 12, "is_published": true, "total_duration_formatted": "0分钟", "description": "暂无描述", "created_at": "", "updated_at": ""}, {"id": "chat-tech-series", "title": "术：聊天技术系列", "icon": "comment", "iconColor": null, "price": 80000, "isFree": false, "isPurchased": false, "isPackage": false, "defaultExpanded": false, "totalVideos": 3, "categories": [{"id": "chat-tech-1", "title": "聊天技巧1", "seriesId": "chat-tech-series", "price": 15000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 1, "videos": [{"id": "chat-tech-1-opening", "title": "01. 开场白的艺术", "description": "掌握各种场景下的开场白技巧。", "duration": 1320, "categoryId": "chat-tech-1", "playCount": 25000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video20.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}, {"id": "chat-tech-2", "title": "聊天技术2", "seriesId": "chat-tech-series", "price": 30000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 1, "videos": [{"id": "chat-tech-2-continuation", "title": "01. 聊天延续技巧", "description": "学会如何让聊天持续进行下去。", "duration": 1680, "categoryId": "chat-tech-2", "playCount": 8000, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video21.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}, {"id": "chat-tech-3", "title": "聊天技术3", "seriesId": "chat-tech-series", "price": 35000, "isFree": false, "isPurchased": false, "defaultExpanded": false, "totalVideos": 1, "videos": [{"id": "chat-tech-3-closing", "title": "01. 聊天收尾艺术", "description": "完美结束聊天的技巧和方法。", "duration": 1450, "categoryId": "chat-tech-3", "playCount": 3200, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video22.mp4", "localPath": null}], "progress": 0.0, "watchCount": 0}], "category_count": 3, "video_count": 3, "is_published": true, "total_duration_formatted": "0分钟", "description": "暂无描述", "created_at": "", "updated_at": ""}], "categories": [{"id": "chat-tech-1", "title": "聊天技巧1", "seriesId": "chat-tech-series", "price": 15000, "isFree": false, "defaultExpanded": false, "videoIds": ["chat-tech-1-opening"], "isPurchased": false, "progress": 0.0, "watchCount": 0, "video_count": 1, "series_title": "术：聊天技术系列", "created_at": "", "updated_at": "", "series_id": "chat-tech-series"}, {"id": "chat-tech-2", "title": "聊天技术2", "seriesId": "chat-tech-series", "price": 30000, "isFree": false, "defaultExpanded": false, "videoIds": ["chat-tech-2-continuation"], "isPurchased": false, "progress": 0.0, "watchCount": 0, "video_count": 1, "series_title": "术：聊天技术系列", "created_at": "", "updated_at": "", "series_id": "chat-tech-series"}, {"id": "chat-tech-3", "title": "聊天技术3", "seriesId": "chat-tech-series", "price": 35000, "isFree": false, "defaultExpanded": false, "videoIds": ["chat-tech-3-closing"], "isPurchased": false, "progress": 0.0, "watchCount": 0, "video_count": 1, "series_title": "术：聊天技术系列", "created_at": "", "updated_at": "", "series_id": "chat-tech-series"}], "videos": [{"id": "chat-tech-3-closing", "title": "01. 聊天收尾艺术", "description": "完美结束聊天的技巧和方法。", "duration": 1450, "categoryId": "chat-tech-3", "playCount": 3200, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video22.mp4", "localPath": null, "series_title": "术：聊天技术系列", "series_id": "chat-tech-series", "category_title": "聊天技术3", "category_id": "chat-tech-3", "created_at": "", "updated_at": ""}], "users": []}, "metadata": {"total_counts": {"series_count": 4, "categories_count": 3, "videos_count": 1, "users_count": 0}, "last_sync": "2025-06-28T23:20:29.784576"}}